import { useGS<PERSON> } from "@gsap/react";
import gsap from "gsap";

const Home = () => {
  // Properly scattered positions - pura screen cover karne ke liye
  const fixedPositions = [
    { top: 5, left: 2 },      // top-left corner
    { top: 8, left: 95 },     // top-right corner
    { top: 15, left: 12 },    // top area
    { top: 22, left: 88 },    // top-right
    { top: 35, left: 5 },     // left side middle
    { top: 42, left: 92 },    // right side middle
    { top: 28, left: 45 },    // center-top
    { top: 55, left: 8 },     // left-middle
    { top: 48, left: 78 },    // right-middle
    { top: 65, left: 25 },    // bottom-left area
    { top: 72, left: 85 },    // bottom-right area
    { top: 38, left: 62 },    // center-right
    { top: 18, left: 35 },    // top-center
    { top: 85, left: 15 },    // bottom-left
    { top: 78, left: 68 },    // bottom-center-right
    { top: 12, left: 58 },    // top-center-right
    { top: 62, left: 42 },    // center-bottom
    { top: 32, left: 28 },    // center-left
    { top: 88, left: 52 },    // bottom-center
    { top: 25, left: 75 },    // top-right area
    { top: 52, left: 18 },    // middle-left
    { top: 75, left: 38 },    // bottom-center-left
    { top: 45, left: 85 },    // middle-right
    { top: 68, left: 62 },    // bottom-center-right
    { top: 82, left: 28 },    // bottom-left area
    { top: 58, left: 72 },    // middle-right area
    { top: 92, left: 45 },    // very bottom center
    { top: 95, left: 82 },    // bottom-right corner
    { top: 90, left: 8 }      // bottom-left corner
  ];

  useGSAP(() => {
    gsap.from(
      ".images, .images img",
      {
        y: 70,
        opacity: 0,
        stagger: 0.2,
        duration: 1,
        ease: "back.out(1.7)",
      },
      [".images", ".images img"]
    );
  });
  
  useGSAP(() => {
    const main = document.querySelector("main");
    main?.addEventListener("mousemove", (e) => {
      const xMove = (e.clientX / window.innerWidth - 0.5) * 40;
    });
  });

  return (
    <>
      <main className="w-full h-screen bg-[#99ca3fef] overflow-hidden">
        <div className="flex w-full relative">
          {[...Array(25)].map((_, index) => {
            const position = fixedPositions[index];
            const images = [
              "https://ik.imagekit.io/wcmq9ntmo/fit%20&%20flex/image-removebg-preview%20(7).png?updatedAt=1753096638609",
              "https://ik.imagekit.io/wcmq9ntmo/fit%20&%20flex/image-removebg-preview%20(5).png?updatedAt=1753096638623",
              "https://ik.imagekit.io/wcmq9ntmo/fit%20&%20flex/image-removebg-preview%20(8).png?updatedAt=1753096638605",
              "https://ik.imagekit.io/wcmq9ntmo/fit%20&%20flex/image-removebg-preview%20(4).png?updatedAt=1753096638715",
              "https://ik.imagekit.io/wcmq9ntmo/fit%20&%20flex/image-removebg-preview%20(9).png?updatedAt=1753096745202",
              "https://ik.imagekit.io/wcmq9ntmo/fit%20&%20flex/image-removebg-preview%20(6).png?updatedAt=1753096638565"
            ];

            // Random sizes for more scattered look
            const sizes = [
              'w-16 h-16', 'w-20 h-20', 'w-24 h-24', 'w-18 h-18', 'w-22 h-22', 'w-28 h-28'
            ];
            const randomSize = sizes[index % sizes.length];

            return (
              <img
                key={index}
                className={`absolute ${randomSize} max-w-none opacity-80 hover:opacity-100 transition-opacity duration-300`}
                style={{
                  top: `${position.top}%`,
                  left: `${position.left}%`,
                  transform: `rotate(${(index * 17) % 360 - 180}deg)`, // Random rotation for scattered effect
                  zIndex: Math.floor(Math.random() * 10) + 1,
                }}
                src={images[index % images.length]}
                alt=""
              />
            );
          })}
        </div>
        <nav className="w-full h-20 ">
          <div className="cursor-pointer">
            <svg
              width="150"
              className="-rotate-12 hover:rotate-0 cursor-pointer transform transition duration-400 ease-in-out"
              height="100"
              xmlns="http://www.w3.org/2000/svg"
            >
              {/* <!-- Even Smaller Yellow Oval --> */}
              <ellipse cx="74" cy="50" rx="60" ry="40" fill="#FFDE17" />

              {/* <!-- MENU Text --> */}
              <text
                x="75"
                y="61"
                text-anchor="middle"
                fill="#400020"
                font-size="30"
                letterSpacing="2"
                font-weight="600"
                font-family="obvisouly"
              >
                MENU
              </text>
            </svg>
          </div>
        </nav>
        <div className="px-4 max-w-full overflow-hidden">
          <div className="images flex w-full pt-4 -space-x-40 justify-center items-center flex-wrap lg:flex-nowrap">
            <span className="w-80 h-80 z-6 flex-shrink-0">
              <img
                loading="lazy"
                className="w-full h-full object-contain"
                src="https://ik.imagekit.io/wcmq9ntmo/fit%20&%20flex/Granola-Mixed-Fruit-275_1100x-removebg-preview.webp?updatedAt=1753078476016"
                alt=""
              />
            </span>

            <span className="w-80 h-80 z-7 mt-3 flex-shrink-0">
              <img
                loading="lazy"
                className="w-full h-full object-contain"
                src="https://ik.imagekit.io/wcmq9ntmo/fit%20&%20flex/Power-Oats-PBC-400_1100x__1_-removebg-preview.webp?updatedAt=1753078475966"
                alt=""
              />
            </span>

            <span className="w-80 h-80 z-8 mt-5 flex-shrink-0">
              <img
                loading="lazy"
                className="w-full h-full object-contain"
                src="https://ik.imagekit.io/wcmq9ntmo/fit%20&%20flex/Muesli-Choco-almond-450_1100x__2_-removebg-preview.webp?updatedAt=1753078475861"
                alt=""
              />
            </span>

            <span className="w-80 h-80 z-7 mt-3 flex-shrink-0">
              <img
                loading="lazy"
                className="w-full h-full object-contain"
                src="https://ik.imagekit.io/wcmq9ntmo/fit%20&%20flex/Power-Oats-PBC-400_1100x__3_-removebg-preview.webp?updatedAt=1753078475948"
                alt=""
              />
            </span>
            <span className="w-55 h-50 mt-8 ml-7 z-6 flex-shrink-0">
              <img
                loading="lazy"
                className="w-full h-full object-contain"
                src="https://ik.imagekit.io/wcmq9ntmo/fit%20&%20flex/HighProteinMuesliFOP_1100x-removebg-preview.webp?updatedAt=1753078475914"
                alt=""
              />
            </span>
          </div>
          <div className="flex flex-col justify-center items-center w-full px-4 max-w-full">
            <h1 className="font-[American_Caption] text-[#3B0017] text-4xl sm:text-6xl lg:text-8xl text-center">
              Fit & Flex
            </h1>
            <div className="flex items-center justify-center flex-wrap lg:flex-nowrap gap-2 lg:gap-0 max-w-full">
              <svg
                className="text-yellow-400 flex-shrink-0"
                id="svg-star"
                width="60"
                height="60"
                viewBox="0 0 118 181"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M58.6134 0C58.6134 0 53.3553 68.0096 0 92.2761C0 92.2761 50.7796 108.196 58.6134 181C66.4428 108.196 117.004 92.2761 117.004 92.2761C63.6447 68.0096 58.6134 0 58.6134 0Z"
                  fill="currentColor"
                ></path>
              </svg>

              <p className="flex gap-1 lg:gap-2 font-[obvisouly] text-[#3B0017] text-lg sm:text-2xl lg:text-[2.6rem] flex-wrap justify-center text-center px-2">
                <span>LIGHTER,</span>
                <span>LEANER</span>
                <span>&</span>
                <span>FULL</span>
                <span>OF</span>
                <span>PROTEIN —</span>
                <span>WAY</span>
                <span>BETTER</span>
                <span>THAN</span>
                <span>POPCORN</span>
              </p>
              <svg
                className="text-yellow-400 flex-shrink-0"
                id="svg-star"
                width="60"
                height="60"
                viewBox="0 0 118 181"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M58.6134 0C58.6134 0 53.3553 68.0096 0 92.2761C0 92.2761 50.7796 108.196 58.6134 181C66.4428 108.196 117.004 92.2761 117.004 92.2761C63.6447 68.0096 58.6134 0 58.6134 0Z"
                  fill="currentColor"
                ></path>
              </svg>
            </div>
          </div>
        </div>
      </main>
    </>
  );
};

export default Home;
