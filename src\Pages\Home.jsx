import { useGS<PERSON> } from "@gsap/react";
import gsap from "gsap";

const Home = () => {
  // Fixed positions array - ye kabhi change nahi hoga
  const fixedPositions = [
    { top: 15, left: 10 },
    { top: 25, left: 75 },
    { top: 45, left: 20 },
    { top: 35, left: 85 },
    { top: 65, left: 15 },
    { top: 55, left: 70 },
    { top: 75, left: 45 },
    { top: 20, left: 50 },
    { top: 80, left: 25 },
    { top: 40, left: 60 },
    { top: 60, left: 80 },
    { top: 30, left: 35 },
    { top: 70, left: 65 },
    { top: 50, left: 90 }
  ];

  useGSAP(() => {
    gsap.from(
      ".images, .images img",
      {
        y: 70,
        opacity: 0,
        stagger: 0.2,
        duration: 1,
        ease: "back.out(1.7)",
      },
      [".images", ".images img"]
    );
  });
  
  useGSAP(() => {
    const main = document.querySelector("main");
    main?.addEventListener("mousemove", (e) => {
      const xMove = (e.clientX / window.innerWidth - 0.5) * 40;
    });
  });

  return (
    <>
      <main className="w-full h-screen bg-[#99ca3fef]">
        <div className="flex w-full ">
          {[...Array(14)].map((_, index) => {
            const position = fixedPositions[index];
            const images = [
              "https://ik.imagekit.io/wcmq9ntmo/fit%20&%20flex/image-removebg-preview%20(7).png?updatedAt=1753096638609",
              "https://ik.imagekit.io/wcmq9ntmo/fit%20&%20flex/image-removebg-preview%20(5).png?updatedAt=1753096638623",
              "https://ik.imagekit.io/wcmq9ntmo/fit%20&%20flex/image-removebg-preview%20(8).png?updatedAt=1753096638605",
              "https://ik.imagekit.io/wcmq9ntmo/fit%20&%20flex/image-removebg-preview%20(4).png?updatedAt=1753096638715",
              "https://ik.imagekit.io/wcmq9ntmo/fit%20&%20flex/image-removebg-preview%20(9).png?updatedAt=1753096745202",
              "https://ik.imagekit.io/wcmq9ntmo/fit%20&%20flex/image-removebg-preview%20(6).png?updatedAt=1753096638565"
            ];
            
            return (
              <img
                key={index}
                className={`absolute ${index === 10 || index === 11 ? 'w-31 h-20' : 'w-20 h-22'}`}
                style={{
                  top: `${position.top}%`,
                  left: `${position.left}%`,
                }}
                src={images[index % images.length]}
                alt=""
              />
            );
          })}
        </div>
        <nav className="w-full h-20 ">
          <div className="cursor-pointer">
            <svg
              width="150"
              className="-rotate-12 hover:rotate-0 cursor-pointer transform transition duration-400 ease-in-out"
              height="100"
              xmlns="http://www.w3.org/2000/svg"
            >
              {/* <!-- Even Smaller Yellow Oval --> */}
              <ellipse cx="74" cy="50" rx="60" ry="40" fill="#FFDE17" />

              {/* <!-- MENU Text --> */}
              <text
                x="75"
                y="61"
                text-anchor="middle"
                fill="#400020"
                font-size="30"
                letterSpacing="2"
                font-weight="600"
                font-family="obvisouly"
              >
                MENU
              </text>
            </svg>
          </div>
        </nav>
        <div className="-ml-20">
          <div className="images flex w-full  pt-4 -space-x-40 justify-center">
            <span className="w-80 h-80 z-6 ">
              <img
                loading="lazy"
                src="https://ik.imagekit.io/wcmq9ntmo/fit%20&%20flex/Granola-Mixed-Fruit-275_1100x-removebg-preview.webp?updatedAt=1753078476016"
                alt=""
              />
            </span>

            <span className="w-80 h-80 z-7 mt-3">
              <img
                loading="lazy"
                src="https://ik.imagekit.io/wcmq9ntmo/fit%20&%20flex/Power-Oats-PBC-400_1100x__1_-removebg-preview.webp?updatedAt=1753078475966"
                alt=""
              />
            </span>

            <span className="w-80 h-80 z-8 mt-5">
              <img
                loading="lazy"
                src="https://ik.imagekit.io/wcmq9ntmo/fit%20&%20flex/Muesli-Choco-almond-450_1100x__2_-removebg-preview.webp?updatedAt=1753078475861"
                alt=""
              />
            </span>

            <span className="w-80 h-80 z-7 mt-3">
              <img
                loading="lazy"
                src="https://ik.imagekit.io/wcmq9ntmo/fit%20&%20flex/Power-Oats-PBC-400_1100x__3_-removebg-preview.webp?updatedAt=1753078475948"
                alt=""
              />
            </span>
            <span className="w-55 h-50 mt-8 ml-7 z-6">
              <img
                loading="lazy"
                src="https://ik.imagekit.io/wcmq9ntmo/fit%20&%20flex/HighProteinMuesliFOP_1100x-removebg-preview.webp?updatedAt=1753078475914"
                alt=""
              />
            </span>
          </div>
          <div className="flex flex-col justify-center items-center w-full pl-25 ">
            <h1 className="font-[American_Caption] text-[#3B0017] text-8xl">
              Fit & Flex
            </h1>
            <div className="flex items-center pr-5 ">
              <svg
                className="text-yellow-400"
                id="svg-star"
                width="100"
                height="100"
                viewBox="0 0 118 181"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M58.6134 0C58.6134 0 53.3553 68.0096 0 92.2761C0 92.2761 50.7796 108.196 58.6134 181C66.4428 108.196 117.004 92.2761 117.004 92.2761C63.6447 68.0096 58.6134 0 58.6134 0Z"
                  fill="currentColor"
                ></path>
              </svg>

              <p className="flex gap-2 font-[obvisouly] text-[#3B0017]    text-[2.6rem]">
                <span>LIGHTER,</span>
                <span>LEANER</span>
                <span>&</span>
                <span>FULL</span>
                <span>OF</span>
                <span>PROTEIN —</span>
                <span>WAY</span>
                <span>BETTER</span>
                <span>THAN</span>
                <span>POPCORN</span>
              </p>
              <svg
                className="text-yellow-400"
                id="svg-star"
                width="100"
                height="100"
                viewBox="0 0 118 181"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M58.6134 0C58.6134 0 53.3553 68.0096 0 92.2761C0 92.2761 50.7796 108.196 58.6134 181C66.4428 108.196 117.004 92.2761 117.004 92.2761C63.6447 68.0096 58.6134 0 58.6134 0Z"
                  fill="currentColor"
                ></path>
              </svg>
            </div>
          </div>
        </div>
      </main>
    </>
  );
};

export default Home;
